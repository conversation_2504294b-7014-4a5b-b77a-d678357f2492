<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.layout.*?>
<?import javafx.scene.control.*?>
<?import javafx.geometry.Insets?>

<BorderPane xmlns="http://javafx.com/javafx/8" xmlns:fx="http://javafx.com/fxml/1"
            fx:controller="main.controller.MainController">

    <top>
        <MenuBar>
            <Menu text="Fichier">
                <MenuItem text="Ouvrir" onAction="#handleOuvrir"/>
                <MenuItem text="Enregistrer" onAction="#handleEnregistrer"/>
            </Menu>
            <Menu text="Édition">
                <MenuItem text="Annuler" onAction="#handleAnnuler"/>
            </Menu>
            <Menu text="Journal">
                <MenuItem text="Journaliser" onAction="#handleJournaliser"/>
            </Menu>
        </MenuBar>
    </top>

    <left>
        <VBox spacing="10" >
            <Label text="Palette"/>
            <Button text="Ajouter Sommet" onAction="#handleAddNode"/>
            <Button text="Ajouter Arête" onAction="#handleAddEdge"/>
        </VBox>
    </left>

    <center>
        <Pane fx:id="drawingPane" prefWidth="600" prefHeight="400" style="-fx-background-color: #f0f0f0;"/>
    </center>

    <right>
        <VBox spacing="10" >
            <Label text="Choisir Méthode"/>
            <ComboBox fx:id="algoComboBox"/>
            <Button text="Calculer Chemin" onAction="#handleCalculer"/>
        </VBox>
    </right>

    <bottom>
        <TextArea fx:id="logArea" prefHeight="100" editable="false"/>
    </bottom>
</BorderPane>
