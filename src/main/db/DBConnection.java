package main.db;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.sql.Statement;

public class DBConnection {
    private static DBConnection instance;
    private Connection conn;

    // Ensure your MySQL server is running and database 'graphdb' exists.
    // Default XAMPP MySQL: user 'root', no password.
    private static final String URL = "************************************************************************************************";
    private static final String USER = "root";
    private static final String PASSWORD = ""; // Default for XAMPP

    private DBConnection() {
        try {
            Class.forName("com.mysql.cj.jdbc.Driver");
            conn = DriverManager.getConnection(URL, USER, PASSWORD);
            System.out.println("✅ Connected to MySQL database 'graphdb' successfully.");
            createActionsTableIfNotExists();
        } catch (ClassNotFoundException e) {
            System.err.println("❌ MySQL JDBC Driver not found. Make sure the MySQL Connector/J JAR is in your classpath.");
            e.printStackTrace();
            conn = null; // Ensure conn is null if connection fails
        } catch (SQLException e) {
            System.err.println("❌ MySQL connection or SQL error. Check connection URL, username, password, and if DB 'graphdb' exists.");
            e.printStackTrace();
            conn = null; // Ensure conn is null if connection fails
        }
    }

    private void createActionsTableIfNotExists() {
        if (conn == null) return; // Don't proceed if connection failed

        try (Statement stmt = conn.createStatement()) {
            String sql = """
                CREATE TABLE IF NOT EXISTS actions (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    action TEXT NOT NULL,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );
            """;
            stmt.executeUpdate(sql);
            System.out.println("🗂️ Table 'actions' checked/created in 'graphdb'.");
        } catch (SQLException e) {
            System.err.println("❌ Failed to create 'actions' table: " + e.getMessage());
            e.printStackTrace();
        }
    }

    public static synchronized DBConnection getInstance() {
        if (instance == null) {
            instance = new DBConnection();
        }
        return instance;
    }

    public Connection getConnection() {
        return conn;
    }
}