package main.controller;

import javafx.fxml.FXML;
import javafx.scene.control.*;
import javafx.scene.input.MouseEvent;
import javafx.scene.layout.Pane;
import javafx.scene.paint.Color;
import javafx.scene.shape.Circle;
import javafx.scene.shape.Line;
import javafx.scene.text.Text;
import main.algo.*;
import main.graph.*;
import main.logger.DBLogger;
import main.logger.FileLogger;
import main.logger.Logger;
import main.pattern.GraphObservable;
import main.pattern.GraphObserver;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

public class MainController {

    @FXML
    private Pane drawingPane;
    @FXML
    private ComboBox<String> algoComboBox;
    @FXML
    private TextArea logArea;

    // Graph model
    private Graph graph = new Graph();
    private int nodeCounter = 0;

    // UI state
    private List<Circle> fxNodes = new ArrayList<>(); // Keep track of JavaFX Circle objects
    private Map<Circle, List<Line>> fxNodeEdges = new HashMap<>(); // For UI updates when dragging
    private List<Circle> selectedFxNodes = new ArrayList<>(); // For creating edges
    
    // Mapping between JavaFX Circles and Graph Nodes
    private Map<Circle, Node> fxCircleToGraphNodeMap = new HashMap<>();
    // private Map<Node, Circle> graphNodeToFxCircleMap = new HashMap<>(); // Can use Node.view

    // Logging
    private GraphObservable graphObservable = new GraphObservable();
    private Logger uiLogger; // For logArea

    @FXML
    public void initialize() {
        // UI Logger - just appends to logArea
        this.uiLogger = message -> logArea.appendText(message + "\n");

        // Setup persistent loggers via Observer pattern
        Logger dbLogger = new DBLogger(); // Attempts to connect to DB
        Logger fileLogger = new FileLogger(); // Logs to log.txt
        
        graphObservable.addObserver(new GraphObserver(dbLogger));
        graphObservable.addObserver(new GraphObserver(fileLogger));
        
        // Check DB connection status from DBLogger's perspective (it uses getInstance)
        if (main.db.DBConnection.getInstance().getConnection() == null) {
            uiLogger.log("⚠️ Connexion à la base de données échouée. Journalisation DB désactivée.");
        } else {
            uiLogger.log("Connexion BD établie. Journalisation DB activée.");
        }
        uiLogger.log("Journalisation Fichier activée (log.txt).");

        algoComboBox.getItems().addAll("Bellman-Ford", "Gauss");
        uiLogger.log("Interface initialisée.");
    }

    @FXML
    private void handleAddNode() {
        double x = 50 + fxNodes.size() * 70; // Spread them out a bit more
        double y = 100;
        if (fxNodes.size() % 5 == 0 && fxNodes.size() > 0) { // Simple layout
             x = 50;
             y += 70;
        }


        Circle fxNodeCircle = new Circle(x, y, 20);
        fxNodeCircle.setStyle("-fx-fill: #2a9df4; -fx-stroke: black; -fx-stroke-width: 1;");
        
        String nodeName = "N" + nodeCounter++;
        Node graphNode = new Node(nodeName, fxNodeCircle);
        graph.addNode(graphNode);

        fxCircleToGraphNodeMap.put(fxNodeCircle, graphNode);
        // graphNodeToFxCircleMap.put(graphNode, fxNodeCircle);

        Text nodeLabel = new Text(x - 5, y + 5, nodeName); // Adjust offset as needed
        nodeLabel.setMouseTransparent(true); // So it doesn't interfere with circle clicks

        drawingPane.getChildren().addAll(fxNodeCircle, nodeLabel);
        fxNodes.add(fxNodeCircle);
        fxNodeEdges.put(fxNodeCircle, new ArrayList<>());

        makeNodeDraggable(fxNodeCircle, nodeLabel);

        String message = "Sommet '" + nodeName + "' ajouté à (" + String.format("%.0f", x) + ", " + String.format("%.0f", y) + ")";
        uiLogger.log(message);
        graphObservable.change(message); // For persistent logging
    }

    @FXML
    private void handleAddEdge() {
        if (selectedFxNodes.size() < 2) {
            uiLogger.log("Veuillez sélectionner deux sommets en cliquant dessus pour créer une arête.");
            uiLogger.log("Les sommets sélectionnés sont en orange. Cliquez à nouveau pour désélectionner.");
        } else {
            uiLogger.log("Deux sommets déjà sélectionnés. Créez l'arête ou désélectionnez-en un.");
        }
    }

    private void makeNodeDraggable(Circle fxCircle, Text label) {
        final double[] dragContext = new double[2]; // offsetX, offsetY

        fxCircle.setOnMousePressed((MouseEvent event) -> {
            dragContext[0] = fxCircle.getCenterX() - event.getSceneX();
            dragContext[1] = fxCircle.getCenterY() - event.getSceneY();

            if (!selectedFxNodes.contains(fxCircle)) {
                if (selectedFxNodes.size() < 2) {
                    selectedFxNodes.add(fxCircle);
                    fxCircle.setStyle("-fx-fill: #f4a261; -fx-stroke: black; -fx-stroke-width: 1;"); // Orange for selected
                }
            } else {
                selectedFxNodes.remove(fxCircle);
                fxCircle.setStyle("-fx-fill: #2a9df4; -fx-stroke: black; -fx-stroke-width: 1;"); // Blue for deselected
            }

            if (selectedFxNodes.size() == 2) {
                Circle fxNodeA = selectedFxNodes.get(0);
                Circle fxNodeB = selectedFxNodes.get(1);
                
                // Prompt for weight
                TextInputDialog dialog = new TextInputDialog("1.0");
                dialog.setTitle("Poids de l'arête");
                dialog.setHeaderText("Entrez le poids pour l'arête entre " + fxCircleToGraphNodeMap.get(fxNodeA).name +
                                     " et " + fxCircleToGraphNodeMap.get(fxNodeB).name);
                dialog.setContentText("Poids:");
                Optional<String> result = dialog.showAndWait();

                result.ifPresent(weightStr -> {
                    try {
                        double weight = Double.parseDouble(weightStr);
                        createEdgeBetween(fxNodeA, fxNodeB, weight);
                    } catch (NumberFormatException e) {
                        uiLogger.log("Poids invalide. L'arête n'a pas été créée.");
                    }
                });

                // Deselect nodes after attempting to create an edge
                fxNodeA.setStyle("-fx-fill: #2a9df4; -fx-stroke: black; -fx-stroke-width: 1;");
                fxNodeB.setStyle("-fx-fill: #2a9df4; -fx-stroke: black; -fx-stroke-width: 1;");
                selectedFxNodes.clear();
            }
            event.consume();
        });

        fxCircle.setOnMouseDragged((MouseEvent event) -> {
            double newX = event.getSceneX() + dragContext[0];
            double newY = event.getSceneY() + dragContext[1];
            fxCircle.setCenterX(newX);
            fxCircle.setCenterY(newY);
            label.setX(newX - 5); // Adjust label position
            label.setY(newY + 5);

            // Update associated graph node's position
            Node graphNode = fxCircleToGraphNodeMap.get(fxCircle);
            if (graphNode != null) {
                graphNode.x = newX;
                graphNode.y = newY;
            }

            List<Line> connectedLines = fxNodeEdges.get(fxCircle);
            if (connectedLines != null) {
                for (Line line : connectedLines) {
                    // Check if this circle is the start or end of the line
                    if (line.getStartX() == graphNode.view.getCenterX() && line.getStartY() == graphNode.view.getCenterY() 
                        && !(Math.abs(line.getStartX() - newX) < 1e-1 && Math.abs(line.getStartY() - newY) < 1e-1) ){
                         // This check is a bit naive, if nodes overlap exactly it might fail.
                         // A better way is to store which end of the line belongs to which node.
                         // For now, this approximation might be okay assuming this circle is the one being dragged.
                        line.setStartX(newX);
                        line.setStartY(newY);
                    } else {
                         line.setEndX(newX);
                         line.setEndY(newY);
                    }
                }
            }
            event.consume();
        });
    }

    private void createEdgeBetween(Circle fxCircleA, Circle fxCircleB, double weight) {
        Node nodeA = fxCircleToGraphNodeMap.get(fxCircleA);
        Node nodeB = fxCircleToGraphNodeMap.get(fxCircleB);

        if (nodeA == null || nodeB == null) {
            uiLogger.log("Erreur: Sommet(s) non trouvés dans le modèle de graphe.");
            return;
        }
        
        // Check if edge already exists (optional, prevents duplicates in model)
        for(Edge existingEdge : graph.edges){
            if((existingEdge.from == nodeA && existingEdge.to == nodeB)){
                uiLogger.log("Arête de " + nodeA.name + " à " + nodeB.name + " existe déjà.");
                // Optionally update weight or just return
                return;
            }
        }

        Line fxLine = new Line(fxCircleA.getCenterX(), fxCircleA.getCenterY(), fxCircleB.getCenterX(), fxCircleB.getCenterY());
        fxLine.setStyle("-fx-stroke: #444; -fx-stroke-width: 2px;");
        fxLine.setMouseTransparent(true); // So it doesn't interfere with node clicks

        // Add to drawing pane, ensure nodes are on top of lines
        drawingPane.getChildren().add(fxLine);
        fxLine.toBack(); // Send line to back

        Edge graphEdge = new Edge(nodeA, nodeB, weight, fxLine);
        graph.addEdge(graphEdge);

        fxNodeEdges.get(fxCircleA).add(fxLine);
        fxNodeEdges.get(fxCircleB).add(fxLine);

        String message = "Arête créée: " + nodeA.name + " -> " + nodeB.name + " (Poids: " + weight + ")";
        uiLogger.log(message);
        graphObservable.change(message);
    }

    @FXML
    private void handleCalculer() {
        String selectedAlgoName = algoComboBox.getValue();
        if (selectedAlgoName == null) {
            uiLogger.log("Veuillez sélectionner une méthode de calcul.");
            return;
        }
        if (graph.nodes.isEmpty()) {
            uiLogger.log("Veuillez ajouter des sommets au graphe avant de calculer.");
            return;
        }

        // Prompt for start node
        List<Node> availableNodes = new ArrayList<>(graph.nodes);
        ChoiceDialog<Node> dialog = new ChoiceDialog<>(availableNodes.get(0), availableNodes);
        dialog.setTitle("Nœud de départ");
        dialog.setHeaderText("Choisissez le nœud de départ pour l'algorithme:");
        dialog.setContentText("Nœud:");

        Optional<Node> resultNode = dialog.showAndWait();
        if (resultNode.isEmpty()) {
            uiLogger.log("Aucun nœud de départ sélectionné. Calcul annulé.");
            return;
        }
        String startNodeName = resultNode.get().name;

        Strategy strategy;
        if (selectedAlgoName.equals("Bellman-Ford")) {
            strategy = new BellmanFord();
        } else if (selectedAlgoName.equals("Gauss")) {
            strategy = new GaussAlgoAdapter(); // Still a mock
        } else {
            uiLogger.log("Algorithme non reconnu: " + selectedAlgoName);
            return;
        }

        String calcMessage = "Calcul du plus court chemin depuis '" + startNodeName + "' avec " + selectedAlgoName + "...";
        uiLogger.log(calcMessage);
        graphObservable.change(calcMessage);

        try {
            Map<String, Double> shortestPaths = strategy.shortestPath(graph, startNodeName);
            uiLogger.log("Résultats:");
            for (Map.Entry<String, Double> entry : shortestPaths.entrySet()) {
                String pathLog = "  Distance vers " + entry.getKey() + ": " + 
                                 (entry.getValue() == Double.POSITIVE_INFINITY ? "Infini" : String.format("%.2f", entry.getValue()));
                uiLogger.log(pathLog);
            }
            graphObservable.change("Calcul terminé. Résultats: " + shortestPaths.toString());

            // Optional: Highlight paths or nodes (more complex UI work)

        } catch (Exception e) {
            uiLogger.log("Erreur durant l'exécution de l'algorithme: " + e.getMessage());
            e.printStackTrace(); // For developer console
            graphObservable.change("Erreur algorithme: " + e.getMessage());
        }
    }
    
    @FXML private void handleOuvrir() { 
        String msg = "Fonction 'Ouvrir' non implémentée.";
        uiLogger.log(msg); 
        graphObservable.change(msg);
    }
    @FXML private void handleEnregistrer() { 
        String msg = "Fonction 'Enregistrer' non implémentée.";
        uiLogger.log(msg); 
        graphObservable.change(msg);
    }
    @FXML private void handleAnnuler() { 
        // A proper undo would require Command Pattern and Memento
        String msg = "Fonction 'Annuler' non implémentée.";
        uiLogger.log(msg); 
        graphObservable.change(msg);
    }
    @FXML private void handleJournaliser() { 
        // This could be used to show logs from DB or toggle logging levels
        String msg = "Fonction 'Journaliser' cliquée. Les journaux sont enregistrés en BD et fichier.";
        uiLogger.log(msg); 
        graphObservable.change(msg);
    }
}