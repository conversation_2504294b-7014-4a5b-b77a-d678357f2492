package main.graph;

import javafx.scene.shape.Line;

public class Edge {
    public Node from, to;
    public double weight;
    public Line view;  // lien avec la ligne affichée

    public Edge(Node from, Node to, double weight) {
        this.from = from;
        this.to = to;
        this.weight = weight;
    }

    public Edge(Node from, Node to, double weight, Line view) {
        this.from = from;
        this.to = to;
        this.weight = weight;
        this.view = view;
    }
    
    @Override
    public String toString() {
        return from.name + " -> " + to.name + " (" + weight + ")";
    }
}