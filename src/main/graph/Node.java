package main.graph;

import javafx.scene.shape.Circle;

public class Node {
    public String name;
    public Circle view;  // lien avec le cercle affiché
    public double x; // Store position for potential save/load
    public double y;

    public Node(String name) {
        this.name = name;
    }

    public Node(String name, Circle view) {
        this.name = name;
        this.view = view;
        if (view != null) {
            this.x = view.getCenterX();
            this.y = view.getCenterY();
        }
    }

    @Override
    public String toString() {
        return name; // Useful for ComboBoxes or lists
    }
}