package main.logger;

import main.db.DBConnection;
import java.sql.*;

public class DBLogger implements Logger {
    @Override
    public void log(String msg) {
        Connection conn = DBConnection.getInstance().getConnection();
        if (conn == null) {
            System.err.println("DBLogger: Cannot log, no database connection.");
            return;
        }
        try (PreparedStatement ps = conn.prepareStatement("INSERT INTO actions (action) VALUES (?)")) {
            ps.setString(1, msg);
            ps.executeUpdate();
        } catch (SQLException e) {
            System.err.println("DBLogger: Error logging to DB: " + e.getMessage());
            // e.printStackTrace(); // Potentially too noisy for repeated failures
        }
    }
}