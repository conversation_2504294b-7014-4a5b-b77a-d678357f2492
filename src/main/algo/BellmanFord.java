package main.algo;

import main.graph.*;
import java.util.*;

public class BellmanFord implements Strategy {
    @Override
    public Map<String, Double> shortestPath(Graph g, String start) {
        Map<String, Double> dist = new HashMap<>();
        for (Node n : g.nodes) dist.put(n.name, Double.POSITIVE_INFINITY);
        dist.put(start, 0.0);

        for (int i = 0; i < g.nodes.size() - 1; i++) {
            for (Edge e : g.edges) {
                if (dist.get(e.from.name) + e.weight < dist.get(e.to.name)) {
                    dist.put(e.to.name, dist.get(e.from.name) + e.weight);
                }
            }
        }
        return dist;
    }
}