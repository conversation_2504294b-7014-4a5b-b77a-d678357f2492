package main.algo;

import main.graph.*;
import java.util.*;

public class GaussAlgoAdapter implements Strategy {
    @Override
    public Map<String, Double> shortestPath(Graph g, String start) {
        // Simuler une autre approche avec "Gauss", ici on retourne un faux résultat
        Map<String, Double> result = new HashMap<>();
        for (Node n : g.nodes) result.put(n.name, Math.random() * 10);
        return result;
    }
}
