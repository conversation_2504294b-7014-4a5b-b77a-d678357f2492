<?xml version="1.0" encoding="UTF-8"?>
<classpath>
	<classpathentry kind="con" path="org.eclipse.jdt.launching.JRE_CONTAINER/org.eclipse.jdt.internal.debug.ui.launcher.StandardVMType/JavaSE-17">
		<attributes>
			<attribute name="maven.pomderived" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry including="**/*.java" kind="src" output="target/classes" path="src">
		<attributes>
			<attribute name="optional" value="true"/>
			<attribute name="maven.pomderived" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="C:/Users/<USER>/Documents/2eme genie logiciel/jfx/javafx-sdk-21.0.7/lib/javafx.base.jar">
		<attributes>
			<attribute name="module" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="C:/Users/<USER>/Documents/2eme genie logiciel/jfx/javafx-sdk-21.0.7/lib/javafx.controls.jar">
		<attributes>
			<attribute name="module" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="C:/Users/<USER>/Documents/2eme genie logiciel/jfx/javafx-sdk-21.0.7/lib/javafx.fxml.jar">
		<attributes>
			<attribute name="module" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="C:/Users/<USER>/Documents/2eme genie logiciel/jfx/javafx-sdk-21.0.7/lib/javafx.graphics.jar">
		<attributes>
			<attribute name="module" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="C:/Users/<USER>/Documents/2eme genie logiciel/jfx/javafx-sdk-21.0.7/lib/javafx.media.jar">
		<attributes>
			<attribute name="module" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="C:/Users/<USER>/Documents/2eme genie logiciel/jfx/javafx-sdk-21.0.7/lib/javafx.swing.jar">
		<attributes>
			<attribute name="module" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="C:/Users/<USER>/Documents/2eme genie logiciel/jfx/javafx-sdk-21.0.7/lib/javafx.web.jar">
		<attributes>
			<attribute name="module" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="C:/Users/<USER>/Documents/2eme genie logiciel/jfx/javafx-sdk-21.0.7/lib/javafx-swt.jar">
		<attributes>
			<attribute name="module" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="C:/Users/<USER>/Documents/2eme genie logiciel/mysql-connector-j-9.3.0/mysql-connector-j-9.3.0.jar">
		<attributes>
			<attribute name="module" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="con" path="org.eclipse.m2e.MAVEN2_CLASSPATH_CONTAINER">
		<attributes>
			<attribute name="maven.pomderived" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="src" path="target/generated-sources/annotations">
		<attributes>
			<attribute name="optional" value="true"/>
			<attribute name="maven.pomderived" value="true"/>
			<attribute name="ignore_optional_problems" value="true"/>
			<attribute name="m2e-apt" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="src" output="target/test-classes" path="target/generated-test-sources/test-annotations">
		<attributes>
			<attribute name="optional" value="true"/>
			<attribute name="maven.pomderived" value="true"/>
			<attribute name="ignore_optional_problems" value="true"/>
			<attribute name="m2e-apt" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="output" path="target/classes"/>
</classpath>
